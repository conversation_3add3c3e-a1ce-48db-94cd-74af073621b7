import { NextRequest, NextResponse } from 'next/server';
import { getAdminTools, updateTool, deleteTool } from '@/lib/supabase';
import { validateApiKey } from '@/lib/auth';

/**
 * GET /api/admin/tools/[id]
 * Get a specific tool for admin (includes drafts and archived)
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Tool ID is required' },
        { status: 400 }
      );
    }

    // Get all admin tools and find the specific one
    // This approach ensures we get tools regardless of content_status
    const result = await getAdminTools({ limit: 1000 });
    const tool = result.data.find(t => t.id === id);

    if (!tool) {
      return NextResponse.json(
        { success: false, error: 'Tool not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: tool,
    });
  } catch (error) {
    console.error('Error fetching admin tool:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch tool' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/tools/[id]
 * Update a specific tool (admin only)
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Tool ID is required' },
        { status: 400 }
      );
    }

    const updates = await request.json();

    // Ensure link field is populated (required by database)
    // If no URL slug provided, generate one from the tool slug/name
    if (!updates.link || !updates.link.trim()) {
      if (updates.slug) {
        updates.link = `/tools/${updates.slug}`;
      } else if (updates.name) {
        updates.link = `/tools/${updates.name.toLowerCase().replace(/\s+/g, '-')}`;
      }
    }

    // Pass updates directly to updateTool - it handles the camelCase to snake_case transformation
    const updatedTool = await updateTool(id, updates);

    return NextResponse.json({
      success: true,
      data: updatedTool,
    });
  } catch (error) {
    console.error('Error updating tool:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update tool' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/tools/[id]
 * Delete a specific tool (admin only)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Tool ID is required' },
        { status: 400 }
      );
    }

    await deleteTool(id);

    return NextResponse.json({
      success: true,
      message: 'Tool deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting tool:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete tool' },
      { status: 500 }
    );
  }
}
